services:
  # 模型数据库
  models_db:
    image: mysql:8.0
    container_name: models_db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-models_root_password}
      MYSQL_DATABASE: ${DB_NAME:-vdb_models}
      MYSQL_USER: ${DB_USER:-models_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-models_password}
    ports:
      - "${DB_PORT:-3306}:3306"
    volumes:
      - /vdb_models/mysql:/var/lib/mysql
      - /vdb_models/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - /vdb_models/backup:/backup
    networks:
      - models_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "${DB_USER:-models_user}", "-p${DB_PASSWORD:-models_password}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # 模型管理服务
  model-manager:
    build:
      context: ./model_manager
      dockerfile: Dockerfile
    container_name: model-manager
    restart: unless-stopped
    ports:
      - "${MANAGER_PORT:-20000}:20000"
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=mysql+pymysql://${DB_USER:-models_user}:${DB_PASSWORD:-models_password}@models_db:3306/${DB_NAME:-vdb_models}
      - SECRET_KEY=${SECRET_KEY:-change-this-secret-key-in-production}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
    volumes:
      - model_logs:/app/logs
      - model_uploads:/app/uploads
    networks:
      - models_network
    depends_on:
      models_db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 模型API服务
  model-api-service:
    build:
      context: ./model_api_service
      dockerfile: Dockerfile
    container_name: model-api-service
    restart: unless-stopped
    ports:
      - "${API_PORT:-20001}:20001"
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=mysql+pymysql://${DB_USER:-models_user}:${DB_PASSWORD:-models_password}@models_db:3306/${DB_NAME:-vdb_models}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - PORT=20001
    volumes:
      - model_api_logs:/app/logs
      - model_api_uploads:/app/uploads
    networks:
      - models_network
    depends_on:
      models_db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:20001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  models_network:
    name: models_network
    driver: bridge

volumes:
  models_data:
    driver: local
  model_logs:
    driver: local
  model_uploads:
    driver: local
  model_api_logs:
    driver: local
  model_api_uploads:
    driver: local
